# <PERSON><PERSON><PERSON> hình <PERSON> MCP cho Cursor

## Phương pháp 1: Sử dụng MCP Extension

### 1. <PERSON><PERSON><PERSON> đặt MCP Extension
1. Mở Cursor
2. Vào Extensions (Ctrl+Shift+X)
3. <PERSON><PERSON><PERSON> kiếm "MCP" hoặc "Model Context Protocol"
4. <PERSON><PERSON>i đặt extension MCP

### 2. <PERSON><PERSON><PERSON> hình MCP Server
1. Mở Command Palette (Ctrl+Shift+P)
2. <PERSON><PERSON><PERSON> "MCP: Configure Servers"
3. Thêm c<PERSON>u hình <PERSON>:

```json
{
  "serena": {
    "command": "docker",
    "args": [
      "run", "--rm", "-i", "--network", "host",
      "-v", "/Users/<USER>/Work/solashi:/workspaces/solashi",
      "-v", "/Users/<USER>/Work/solashi/autotrader:/workspaces/autotrader",
      "serena:latest",
      "serena", "start-mcp-server",
      "--transport", "stdio",
      "--context", "ide-assistant"
    ]
  }
}
```

## Phương pháp 2: Sử dụng SSE Mode

### 1. Khởi động Serena trong SSE mode
```bash
./start-serena.sh start
```

### 2. Cấu hình Cursor để kết nối SSE
1. Mở Cursor Settings
2. Tìm "MCP Servers" hoặc "External Tools"
3. Thêm server URL: `http://localhost:9121/sse`

## Phương pháp 3: Sử dụng qua Cline Extension

### 1. Cài đặt Cline Extension
1. Mở Cursor
2. Vào Extensions
3. Tìm và cài đặt "Cline"

### 2. Cấu hình Cline với MCP
1. Mở Cline panel
2. Vào Settings
3. Thêm MCP server configuration:

```json
{
  "mcpServers": {
    "serena": {
      "command": "docker",
      "args": [
        "run", "--rm", "-i", "--network", "host",
        "-v", "/Users/<USER>/Work/solashi:/workspaces/solashi",
        "-v", "/Users/<USER>/Work/solashi/autotrader:/workspaces/autotrader",
        "serena:latest",
        "serena", "start-mcp-server",
        "--transport", "stdio",
        "--context", "ide-assistant"
      ]
    }
  }
}
```

## Sử dụng

### Kích hoạt project trong Cursor/Cline:
```
Activate the project /workspaces/autotrader
```

### Các lệnh hữu ích:
```
# Phân tích code
Get symbols overview for /workspaces/autotrader/src

# Tìm kiếm
Search for pattern "trading" in /workspaces/autotrader

# Đọc file
Read file /workspaces/autotrader/main.py
```

## Lưu ý
- Đảm bảo Serena MCP server đang chạy
- Sử dụng context `ide-assistant` cho IDE integration
- Restart Cursor sau khi cấu hình MCP
