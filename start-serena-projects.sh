#!/bin/bash

# Serena MCP Server for Projects Management Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Docker compose file for projects
COMPOSE_FILE="docker-compose.projects.yml"

# Function to check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker Desktop first."
        exit 1
    fi
}

# Function to check if compose file exists
check_compose_file() {
    if [ ! -f "$COMPOSE_FILE" ]; then
        print_error "Docker compose file '$COMPOSE_FILE' not found!"
        print_status "Please copy and customize docker-compose.projects.yml for your projects."
        exit 1
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start Serena MCP server for projects (production mode)"
    echo "  dev       Start Serena MCP server for projects (development mode)"
    echo "  stop      Stop Serena MCP server"
    echo "  restart   Restart Serena MCP server"
    echo "  logs      Show Serena logs"
    echo "  status    Show Serena status"
    echo "  build     Build Serena Docker image"
    echo "  clean     Clean up Docker containers and images"
    echo "  edit      Edit the projects compose file"
    echo "  help      Show this help message"
}

# Function to start Serena for projects
start_serena() {
    print_status "Starting Serena MCP server for projects..."
    check_docker
    check_compose_file
    
    # Check if already running
    if docker compose -f "$COMPOSE_FILE" ps serena-projects | grep -q "Up"; then
        print_warning "Serena projects is already running!"
        return 0
    fi
    
    docker compose -f "$COMPOSE_FILE" up -d serena-projects
    
    # Wait a moment for startup
    sleep 3
    
    # Check if started successfully
    if docker compose -f "$COMPOSE_FILE" ps serena-projects | grep -q "Up"; then
        print_success "Serena MCP server for projects started successfully!"
        print_status "Dashboard: http://localhost:${SERENA_DASHBOARD_PORT:-24282}/dashboard/"
        print_status "MCP Server: http://localhost:${SERENA_PORT:-9121}"
        print_status "Container: serena-mcp-projects"
    else
        print_error "Failed to start Serena MCP server for projects"
        docker compose -f "$COMPOSE_FILE" logs serena-projects
        exit 1
    fi
}

# Function to start development mode
start_dev() {
    print_status "Starting Serena MCP server for projects in development mode..."
    check_docker
    check_compose_file
    
    if docker compose -f "$COMPOSE_FILE" ps serena-projects-dev | grep -q "Up"; then
        print_warning "Serena projects dev is already running!"
        return 0
    fi
    
    docker compose -f "$COMPOSE_FILE" up -d serena-projects-dev
    sleep 3
    
    if docker compose -f "$COMPOSE_FILE" ps serena-projects-dev | grep -q "Up"; then
        print_success "Serena MCP server for projects (dev) started successfully!"
        print_status "Dashboard: http://localhost:${SERENA_DASHBOARD_PORT:-24283}/dashboard/"
        print_status "MCP Server: http://localhost:${SERENA_PORT:-9122}"
        print_status "Container: serena-mcp-projects-dev"
    else
        print_error "Failed to start Serena MCP server for projects (dev)"
        docker compose -f "$COMPOSE_FILE" logs serena-projects-dev
        exit 1
    fi
}

# Function to stop Serena
stop_serena() {
    print_status "Stopping Serena MCP server for projects..."
    check_compose_file
    docker compose -f "$COMPOSE_FILE" down
    print_success "Serena MCP server for projects stopped"
}

# Function to restart Serena
restart_serena() {
    print_status "Restarting Serena MCP server for projects..."
    stop_serena
    sleep 2
    start_serena
}

# Function to show logs
show_logs() {
    print_status "Showing Serena projects logs..."
    check_compose_file
    docker compose -f "$COMPOSE_FILE" logs -f
}

# Function to show status
show_status() {
    print_status "Serena MCP server for projects status:"
    check_compose_file
    docker compose -f "$COMPOSE_FILE" ps
    echo ""
    print_status "Port usage:"
    lsof -i :${SERENA_PORT:-9121} 2>/dev/null || echo "Port ${SERENA_PORT:-9121} is free"
    lsof -i :${SERENA_DASHBOARD_PORT:-24282} 2>/dev/null || echo "Port ${SERENA_DASHBOARD_PORT:-24282} is free"
    lsof -i :9122 2>/dev/null || echo "Port 9122 (dev) is free"
    lsof -i :24283 2>/dev/null || echo "Port 24283 (dev dashboard) is free"
}

# Function to build image
build_image() {
    print_status "Building Serena Docker image..."
    docker compose build
    print_success "Serena Docker image built successfully"
}

# Function to clean up
clean_up() {
    print_status "Cleaning up Docker containers and images for projects..."
    check_compose_file
    docker compose -f "$COMPOSE_FILE" down --rmi all --volumes --remove-orphans
    print_success "Cleanup completed"
}

# Function to edit compose file
edit_compose() {
    print_status "Opening projects compose file for editing..."
    if command -v code >/dev/null 2>&1; then
        code "$COMPOSE_FILE"
    elif command -v nano >/dev/null 2>&1; then
        nano "$COMPOSE_FILE"
    elif command -v vim >/dev/null 2>&1; then
        vim "$COMPOSE_FILE"
    else
        print_error "No suitable editor found. Please edit $COMPOSE_FILE manually."
        exit 1
    fi
}

# Main script logic
case "${1:-help}" in
    start)
        start_serena
        ;;
    dev)
        start_dev
        ;;
    stop)
        stop_serena
        ;;
    restart)
        restart_serena
        ;;
    logs)
        show_logs
        ;;
    status)
        show_status
        ;;
    build)
        build_image
        ;;
    clean)
        clean_up
        ;;
    edit)
        edit_compose
        ;;
    help|--help|-h)
        show_usage
        ;;
    *)
        print_error "Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
