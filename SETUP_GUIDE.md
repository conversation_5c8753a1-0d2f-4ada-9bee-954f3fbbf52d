# Hướng dẫn Setup Serena MCP cho các dự án khác

## Tổng quan

Serena MCP đã được cấu hình để làm việc với các dự án khác thông qua Docker volumes. Bạn có thể mount các thư mục dự án của mình vào container để Serena có thể truy cập và làm việc với chúng.

## Các file quan trọng đã được tạo

1. **compose.yaml** - Docker Compose file chính với volume mounts cơ bản
2. **docker-compose.projects.yml** - Template cho việc mount các dự án cụ thể
3. **start-serena.sh** - Script quản lý Serena MCP server cơ bản
4. **start-serena-projects.sh** - Script quản lý Serena cho các dự án cụ thể
5. **claude_desktop_config.json** - <PERSON><PERSON><PERSON> hình mẫu cho Claude Desktop
6. **.env** - File environment variables

## Cấu hình Volume Mounting

### 1. Chỉnh sửa file `compose.yaml`

Trong file `compose.yaml`, bạn có thể thêm các volume mounts cho dự án của mình:

```yaml
volumes:
  # Mount your projects here - customize these paths
  - "${HOME}/Work:/workspaces/projects"  # Mount your main projects directory
  - "${HOME}/Documents/code:/workspaces/code"  # Additional code directory
  # Add more project directories as needed:
  # - "/path/to/your/project1:/workspaces/project1"
  # - "/path/to/your/project2:/workspaces/project2"
```

### 2. Cấu hình Environment Variables

Chỉnh sửa file `.env` để cấu hình ports và paths:

```bash
SERENA_PORT=9121
SERENA_DASHBOARD_PORT=24282
```

## Sử dụng Script Quản lý

### Cách 1: Sử dụng script cơ bản (start-serena.sh)

```bash
# Production mode
./start-serena.sh start

# Development mode (với source code mounted)
./start-serena.sh dev
```

### Cách 2: Sử dụng script cho projects (start-serena-projects.sh)

**Khuyến nghị sử dụng cách này để làm việc với các dự án khác**

```bash
# Chỉnh sửa file docker-compose.projects.yml trước
./start-serena-projects.sh edit

# Production mode cho projects
./start-serena-projects.sh start

# Development mode cho projects
./start-serena-projects.sh dev
```

### Các lệnh khác

```bash
# Dừng Serena
./start-serena.sh stop

# Restart Serena
./start-serena.sh restart

# Xem logs
./start-serena.sh logs

# Kiểm tra status
./start-serena.sh status

# Build lại image
./start-serena.sh build

# Dọn dẹp
./start-serena.sh clean
```

## Cấu hình MCP Client

### Claude Desktop

1. Mở Claude Desktop
2. Vào File > Settings > Developer > MCP Servers > Edit Config
3. Copy nội dung từ file `claude_desktop_config.json` vào config
4. **Quan trọng**: Thay đổi đường dẫn volume mounts cho phù hợp với hệ thống của bạn:
   ```json
   "-v", "/Users/<USER>/Work:/workspaces/projects",
   "-v", "/Users/<USER>/Documents/code:/workspaces/code",
   ```
5. Restart Claude Desktop

### Các MCP Client khác (Cline, Cursor, etc.)

Sử dụng cấu hình tương tự với context `ide-assistant`:

```json
{
  "command": "docker",
  "args": [
    "run", "--rm", "-i", "--network", "host",
    "-v", "/path/to/your/projects:/workspaces/projects",
    "serena:latest",
    "serena", "start-mcp-server",
    "--transport", "stdio",
    "--context", "ide-assistant"
  ]
}
```

## Truy cập Dashboard

Sau khi khởi động, bạn có thể truy cập:
- **Dashboard**: http://localhost:24282/dashboard/
- **MCP Server**: http://localhost:9121 (SSE mode)

## Làm việc với Projects

### Kích hoạt Project

Trong chat với Claude/MCP client, bạn có thể kích hoạt project bằng cách:

```
Activate the project /workspaces/projects/my-project
```

hoặc nếu đã kích hoạt trước đó:

```
Activate the project my-project
```

### Index Project (Khuyến nghị)

Để tăng tốc độ, hãy index các project lớn:

```bash
# Từ trong container
docker exec -it serena-serena-1 bash
cd /workspaces/projects/my-project
uv run --directory /workspaces/serena serena project index
```

## Troubleshooting

### Port đã được sử dụng

```bash
# Kiểm tra port đang được sử dụng
lsof -i :9121
lsof -i :24282

# Thay đổi port trong .env file
SERENA_PORT=9122
SERENA_DASHBOARD_PORT=24283
```

### Container không khởi động

```bash
# Xem logs
./start-serena.sh logs

# Rebuild image
./start-serena.sh build
```

### Project không thể truy cập

1. Kiểm tra volume mounts trong `compose.yaml`
2. Đảm bảo đường dẫn tồn tại trên host
3. Kiểm tra permissions của thư mục

## Ví dụ Cấu hình cho các dự án cụ thể

### Dự án Node.js

```yaml
volumes:
  - "${HOME}/Work/my-node-app:/workspaces/my-node-app"
```

### Dự án Python

```yaml
volumes:
  - "${HOME}/Work/my-python-app:/workspaces/my-python-app"
```

### Multiple Projects

```yaml
volumes:
  - "${HOME}/Work:/workspaces/work"
  - "${HOME}/Projects:/workspaces/projects"
  - "${HOME}/Code:/workspaces/code"
```

## Lưu ý quan trọng

1. **Backup**: Luôn backup code trước khi sử dụng Serena
2. **Git**: Sử dụng Git để theo dõi thay đổi
3. **Permissions**: Đảm bảo Docker có quyền truy cập vào thư mục dự án
4. **Line endings**: Trên Windows, cấu hình `git config core.autocrlf true`

## Tính năng nâng cao

### Custom Contexts và Modes

Bạn có thể tạo custom contexts và modes:

```bash
# Tạo custom context
uvx --from git+https://github.com/oraios/serena serena context create my-context

# Tạo custom mode
uvx --from git+https://github.com/oraios/serena serena mode create my-mode
```

### Memory System

Serena có hệ thống memory để lưu trữ thông tin về project:
- Memories được lưu trong `.serena/memories/` của mỗi project
- Có thể chỉnh sửa và thêm memories thủ công

### Onboarding Process

Lần đầu sử dụng với project mới, Serena sẽ thực hiện onboarding để hiểu cấu trúc project và tạo memories.
