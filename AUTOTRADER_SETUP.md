# Hướng dẫn sử dụng Serena MCP với dự án Autotrader

## ✅ Setup đã hoàn thành

Serena MCP đã được cấu hình để làm việc với dự án autotrader của bạn:

- **Dự án autotrader**: `/Users/<USER>/Work/solashi/autotrader` → `/workspaces/autotrader`
- **Th<PERSON> mục solashi**: `/Users/<USER>/Work/solashi` → `/workspaces/projects`
- **MCP Server**: http://localhost:9121 (SSE transport)
- **Dashboard**: http://localhost:24282/dashboard/

## 📁 Files quan trọng

- **`compose.yaml`** - Docker Compose configuration với volume mounts
- **`start-serena.sh`** - Script quản lý Serena MCP server
- **`claude_desktop_config.json`** - C<PERSON>u hình mẫu cho <PERSON>
- **`.env`** - Environment variables

## 🚀 Cách sử dụng

### 1. Khởi động Serena MCP Server

```bash
cd /Users/<USER>/Work/solashi/serena
./start-serena.sh start
```

### 2. Cấu hình Claude Desktop

Copy nội dung từ file `claude_desktop_config.json` vào cấu hình Claude Desktop:

1. Mở Claude Desktop
2. File > Settings > Developer > MCP Servers > Edit Config
3. Paste cấu hình và restart Claude Desktop

### 3. Kích hoạt dự án Autotrader trong Claude

Trong chat với Claude, sử dụng một trong các lệnh sau:

```
Activate the project /workspaces/autotrader
```

hoặc

```
Activate the project autotrader
```

### 4. Các lệnh hữu ích

#### Khám phá cấu trúc dự án
```
List the directory structure of /workspaces/autotrader
```

#### Xem tổng quan về symbols
```
Get symbols overview for /workspaces/autotrader/src
```

#### Tìm kiếm code
```
Search for pattern "trading" in /workspaces/autotrader
```

#### Đọc file cấu hình
```
Read file /workspaces/autotrader/main.py
```

## 📁 Cấu trúc dự án Autotrader đã được mount

```
/workspaces/autotrader/
├── configs/          # Cấu hình
├── credentials/      # Thông tin xác thực
├── data/            # Dữ liệu
├── docs/            # Tài liệu
├── examples/        # Ví dụ
├── logs/            # Log files
├── scripts/         # Scripts
├── src/             # Source code
├── tests/           # Tests
├── main.py          # File chính
├── requirements.txt # Dependencies
└── docker-compose.yml
```

## 🛠️ Các tác vụ có thể thực hiện với Serena

### Phân tích code
- Hiểu cấu trúc dự án autotrader
- Tìm kiếm functions, classes, variables
- Phân tích dependencies và relationships
- Review code quality

### Chỉnh sửa code
- Sửa bugs
- Thêm features mới
- Refactor code
- Cập nhật documentation

### Testing và debugging
- Chạy tests
- Debug issues
- Kiểm tra logs
- Validate configurations

### DevOps tasks
- Cập nhật Docker configurations
- Quản lý dependencies
- Setup CI/CD
- Environment management

## 💡 Ví dụ sử dụng thực tế

### Ví dụ 1: Phân tích main.py
```
Please analyze the main.py file in the autotrader project and explain what it does
```

### Ví dụ 2: Tìm trading strategies
```
Find all trading strategy implementations in the autotrader project
```

### Ví dụ 3: Kiểm tra configuration
```
Review the configuration files in /workspaces/autotrader/configs and explain the setup
```

### Ví dụ 4: Debug logging
```
Check the recent logs in /workspaces/autotrader/logs and identify any errors
```

### Ví dụ 5: Add new feature
```
I want to add a new trading indicator. Please help me:
1. Analyze the existing indicator structure
2. Create a new RSI indicator
3. Integrate it with the main trading logic
```

## 🔧 Quản lý Serena

### Kiểm tra status
```bash
./start-serena.sh status
```

### Xem logs
```bash
./start-serena.sh logs
```

### Restart server
```bash
./start-serena.sh restart
```

### Dừng server
```bash
./start-serena.sh stop
```

## 📊 Dashboard và Monitoring

Truy cập dashboard tại: http://localhost:24282/dashboard/

Dashboard cung cấp:
- Real-time logs
- Tool usage statistics
- Server status
- Shutdown controls

## 🚨 Lưu ý quan trọng

1. **Backup**: Luôn backup code trước khi chỉnh sửa
2. **Git**: Sử dụng Git để track changes
3. **Testing**: Test thoroughly sau khi chỉnh sửa
4. **Permissions**: Đảm bảo Docker có quyền truy cập thư mục dự án

## 🎯 Best Practices

### Khi làm việc với Serena:

1. **Bắt đầu với phân tích**: Hiểu cấu trúc dự án trước khi chỉnh sửa
2. **Sử dụng symbolic tools**: Tận dụng khả năng phân tích semantic của Serena
3. **Test incrementally**: Test từng thay đổi nhỏ
4. **Use memories**: Để Serena ghi nhớ thông tin quan trọng về dự án
5. **Clean git state**: Bắt đầu từ clean git state để dễ track changes

### Prompting tips:

- Cụ thể về file/directory paths
- Yêu cầu explanation trước khi chỉnh sửa
- Sử dụng step-by-step approach cho tasks phức tạp
- Ask for code review sau khi chỉnh sửa

## 🔄 Workflow đề xuất

1. **Activate project**: `Activate the project autotrader`
2. **Understand**: Phân tích cấu trúc và hiểu dự án
3. **Plan**: Lập kế hoạch cho task cần thực hiện
4. **Implement**: Thực hiện changes với Serena
5. **Test**: Chạy tests và validate
6. **Review**: Review changes và commit

Bây giờ bạn đã sẵn sàng sử dụng Serena MCP với dự án autotrader! 🚀
