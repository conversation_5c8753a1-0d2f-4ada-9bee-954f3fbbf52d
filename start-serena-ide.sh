#!/bin/bash

# Serena MCP Server for IDE Integration (Augment Code, Cursor, etc.)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker Desktop first."
        exit 1
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start Serena MCP server for IDE integration"
    echo "  stop      Stop Serena MCP server"
    echo "  restart   Restart Serena MCP server"
    echo "  status    Show Serena status"
    echo "  logs      Show <PERSON> logs"
    echo "  config    Show IDE configuration examples"
    echo "  help      Show this help message"
}

# Function to start Serena for IDE
start_serena_ide() {
    print_status "Starting Serena MCP server for IDE integration..."
    check_docker
    
    # Check if already running
    if docker compose ps serena | grep -q "Up"; then
        print_warning "Serena is already running!"
        return 0
    fi
    
    # Start with IDE-optimized settings
    SERENA_PORT=9121 SERENA_DASHBOARD_PORT=24282 docker compose up -d serena
    
    # Wait a moment for startup
    sleep 3
    
    # Check if started successfully
    if docker compose ps serena | grep -q "Up"; then
        print_success "Serena MCP server started successfully for IDE integration!"
        print_status "MCP Server (SSE): http://localhost:9121/sse"
        print_status "MCP Server (stdio): Use Docker command from config files"
        print_status "Dashboard: http://localhost:24282/dashboard/"
        print_status "Context: ide-assistant (optimized for IDE integration)"
        echo ""
        print_status "Next steps:"
        echo "  1. Configure your IDE with MCP server settings"
        echo "  2. Use: ./start-serena-ide.sh config to see configuration examples"
        echo "  3. In your IDE, activate project: 'Activate the project /workspaces/autotrader'"
    else
        print_error "Failed to start Serena MCP server"
        docker compose logs serena
        exit 1
    fi
}

# Function to show configuration examples
show_config() {
    print_status "IDE Configuration Examples:"
    echo ""
    echo "=== For Augment Code ==="
    echo "SSE Mode: http://localhost:9121/sse"
    echo "Context: ide-assistant"
    echo ""
    echo "=== For Cursor (with Cline) ==="
    echo "See: cursor_mcp_setup.md"
    echo ""
    echo "=== Docker Command (stdio) ==="
    echo "Command: docker"
    echo "Args: [\"run\", \"--rm\", \"-i\", \"--network\", \"host\","
    echo "       \"-v\", \"/Users/<USER>/Work/solashi:/workspaces/solashi\","
    echo "       \"-v\", \"/Users/<USER>/Work/solashi/autotrader:/workspaces/autotrader\","
    echo "       \"serena:latest\", \"serena\", \"start-mcp-server\","
    echo "       \"--transport\", \"stdio\", \"--context\", \"ide-assistant\"]"
    echo ""
    echo "=== Configuration Files ==="
    echo "- mcp_ide_config.json (general IDE config)"
    echo "- cursor_mcp_setup.md (Cursor specific)"
    echo "- augment_mcp_setup.md (Augment Code specific)"
}

# Function to stop Serena
stop_serena() {
    print_status "Stopping Serena MCP server..."
    docker compose down
    print_success "Serena MCP server stopped"
}

# Function to restart Serena
restart_serena() {
    print_status "Restarting Serena MCP server for IDE..."
    stop_serena
    sleep 2
    start_serena_ide
}

# Function to show status
show_status() {
    print_status "Serena MCP server status:"
    docker compose ps
    echo ""
    print_status "Port usage:"
    lsof -i :9121 2>/dev/null || echo "Port 9121 is free"
    lsof -i :24282 2>/dev/null || echo "Port 24282 is free"
    echo ""
    print_status "IDE Integration URLs:"
    echo "  SSE: http://localhost:9121/sse"
    echo "  Dashboard: http://localhost:24282/dashboard/"
}

# Function to show logs
show_logs() {
    print_status "Showing Serena logs..."
    docker compose logs -f serena
}

# Main script logic
case "${1:-help}" in
    start)
        start_serena_ide
        ;;
    stop)
        stop_serena
        ;;
    restart)
        restart_serena
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    config)
        show_config
        ;;
    help|--help|-h)
        show_usage
        ;;
    *)
        print_error "Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
