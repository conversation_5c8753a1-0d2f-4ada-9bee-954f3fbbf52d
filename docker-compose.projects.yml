# Docker Compose file for mounting specific projects
# Copy this file and customize the volume mounts for your specific projects

services:
  serena-projects:
    image: serena:latest
    build:
      context: ./
      dockerfile: Dockerfile
      target: production
    container_name: serena-mcp-projects
    ports:
      - "${SERENA_PORT:-9121}:9121"
      - "${SERENA_DASHBOARD_PORT:-24282}:24282"
    environment:
      - SERENA_DOCKER=1
    volumes:
      # Solashi projects - your main working directory
      - "${HOME}/Work/solashi:/workspaces/solashi"

      # Specific project mounts for better performance
      - "${HOME}/Work/solashi/autotrader:/workspaces/autotrader"
      - "${HOME}/Work/solashi/serena:/workspaces/serena"

      # Other Solashi projects (add as needed)
      # - "${HOME}/Work/solashi/other-project:/workspaces/other-project"

      # General project directories
      - "${HOME}/Work:/workspaces/work"
      - "${HOME}/Projects:/workspaces/projects"
      - "${HOME}/Code:/workspaces/code"
      
      # Configuration files (optional)
      - "${HOME}/.gitconfig:/root/.gitconfig:ro"
      - "${HOME}/.ssh:/root/.ssh:ro"
      
    command:
      - "uv"
      - "run"
      - "--directory"
      - "."
      - "serena-mcp-server"
      - "--transport"
      - "sse"
      - "--port"
      - "9121"
      - "--host"
      - "0.0.0.0"
    restart: unless-stopped
    
  # Development version with more tools
  serena-projects-dev:
    image: serena:dev
    build:
      context: ./
      dockerfile: Dockerfile
      target: development
    container_name: serena-mcp-projects-dev
    tty: true
    stdin_open: true
    ports:
      - "${SERENA_PORT:-9122}:9121"  # Different port for dev
      - "${SERENA_DASHBOARD_PORT:-24283}:24282"  # Different port for dev
    environment:
      - SERENA_DOCKER=1
    volumes:
      # Mount Serena source code for development
      - .:/workspaces/serena
      
      # Same project mounts as production
      - "${HOME}/Work/solashi:/workspaces/solashi"
      - "${HOME}/Work/solashi/autotrader:/workspaces/autotrader"
      - "${HOME}/Work/solashi/serena:/workspaces/serena"
      - "${HOME}/Work:/workspaces/work"
      - "${HOME}/Projects:/workspaces/projects"
      - "${HOME}/Code:/workspaces/code"
      - "${HOME}/.gitconfig:/root/.gitconfig:ro"
      - "${HOME}/.ssh:/root/.ssh:ro"
      
    command:
      - "uv"
      - "run"
      - "--directory"
      - "."
      - "serena-mcp-server"
    restart: unless-stopped
