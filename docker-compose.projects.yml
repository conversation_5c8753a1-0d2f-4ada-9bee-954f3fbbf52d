# Docker Compose file for mounting specific projects
# Copy this file and customize the volume mounts for your specific projects

version: '3.8'

services:
  serena-projects:
    image: serena:latest
    container_name: serena-mcp-projects
    ports:
      - "${SERENA_PORT:-9121}:9121"
      - "${SERENA_DASHBOARD_PORT:-24282}:24282"
    environment:
      - SERENA_DOCKER=1
    volumes:
      # Example project mounts - customize these for your projects
      
      # Web Development Projects
      - "${HOME}/Work/my-react-app:/workspaces/my-react-app"
      - "${HOME}/Work/my-nextjs-app:/workspaces/my-nextjs-app"
      - "${HOME}/Work/my-vue-app:/workspaces/my-vue-app"
      
      # Backend Projects
      - "${HOME}/Work/my-node-api:/workspaces/my-node-api"
      - "${HOME}/Work/my-python-api:/workspaces/my-python-api"
      - "${HOME}/Work/my-go-service:/workspaces/my-go-service"
      
      # Mobile Projects
      - "${HOME}/Work/my-react-native-app:/workspaces/my-react-native-app"
      - "${HOME}/Work/my-flutter-app:/workspaces/my-flutter-app"
      
      # Data Science Projects
      - "${HOME}/Work/my-ml-project:/workspaces/my-ml-project"
      - "${HOME}/Work/my-data-analysis:/workspaces/my-data-analysis"
      
      # General project directories
      - "${HOME}/Work:/workspaces/work"
      - "${HOME}/Projects:/workspaces/projects"
      - "${HOME}/Code:/workspaces/code"
      - "${HOME}/Documents/GitHub:/workspaces/github"
      
      # Configuration files (optional)
      - "${HOME}/.gitconfig:/root/.gitconfig:ro"
      - "${HOME}/.ssh:/root/.ssh:ro"
      
    command:
      - "uv run --directory . serena-mcp-server --transport sse --port 9121 --host 0.0.0.0"
    restart: unless-stopped
    
  # Development version with more tools
  serena-projects-dev:
    image: serena:dev
    container_name: serena-mcp-projects-dev
    tty: true
    stdin_open: true
    ports:
      - "${SERENA_PORT:-9122}:9121"  # Different port for dev
      - "${SERENA_DASHBOARD_PORT:-24283}:24282"  # Different port for dev
    environment:
      - SERENA_DOCKER=1
    volumes:
      # Mount Serena source code for development
      - .:/workspaces/serena
      
      # Same project mounts as production
      - "${HOME}/Work/my-react-app:/workspaces/my-react-app"
      - "${HOME}/Work/my-nextjs-app:/workspaces/my-nextjs-app"
      - "${HOME}/Work/my-vue-app:/workspaces/my-vue-app"
      - "${HOME}/Work/my-node-api:/workspaces/my-node-api"
      - "${HOME}/Work/my-python-api:/workspaces/my-python-api"
      - "${HOME}/Work/my-go-service:/workspaces/my-go-service"
      - "${HOME}/Work/my-react-native-app:/workspaces/my-react-native-app"
      - "${HOME}/Work/my-flutter-app:/workspaces/my-flutter-app"
      - "${HOME}/Work/my-ml-project:/workspaces/my-ml-project"
      - "${HOME}/Work/my-data-analysis:/workspaces/my-data-analysis"
      - "${HOME}/Work:/workspaces/work"
      - "${HOME}/Projects:/workspaces/projects"
      - "${HOME}/Code:/workspaces/code"
      - "${HOME}/Documents/GitHub:/workspaces/github"
      - "${HOME}/.gitconfig:/root/.gitconfig:ro"
      - "${HOME}/.ssh:/root/.ssh:ro"
      
    command:
      - "uv run --directory . serena-mcp-server"
    restart: unless-stopped
