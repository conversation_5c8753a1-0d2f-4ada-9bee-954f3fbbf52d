# <PERSON><PERSON><PERSON> hình Serena MCP cho Augment Code

## Phương pháp 1: Sử dụng MCP Integration (Khuyến nghị)

### 1. Khởi động Serena MCP Server
```bash
./start-serena.sh start
```

### 2. <PERSON><PERSON>u hình trong Augment Code
1. Mở Augment Code
2. Vào Settings/Preferences
3. Tìm "MCP Servers" hoặc "External Tools"
4. Thêm server mới:

**Server Name:** Serena
**Server URL:** `http://localhost:9121/sse`
**Transport:** SSE
**Context:** ide-assistant

### 3. Hoặc sử dụng Docker command
Nếu Augment Code hỗ trợ stdio transport:

```json
{
  "name": "serena",
  "command": "docker",
  "args": [
    "run", "--rm", "-i", "--network", "host",
    "-v", "/Users/<USER>/Work/solashi:/workspaces/solashi",
    "-v", "/Users/<USER>/Work/solashi/autotrader:/workspaces/autotrader",
    "serena:latest",
    "serena", "start-mcp-server",
    "--transport", "stdio",
    "--context", "ide-assistant"
  ]
}
```

## Phương pháp 2: Sử dụng qua Extension

### 1. Tìm MCP Extension
1. Mở Extension Marketplace trong Augment Code
2. Tìm kiếm "MCP" hoặc "Model Context Protocol"
3. Cài đặt extension phù hợp

### 2. Cấu hình Extension
1. Sau khi cài đặt, mở extension settings
2. Thêm Serena MCP server configuration
3. Sử dụng cấu hình từ file `mcp_ide_config.json`

## Sử dụng trong Augment Code

### 1. Kích hoạt Serena Tools
Sau khi cấu hình, bạn sẽ thấy Serena tools trong:
- Command Palette
- Context menu
- AI Assistant panel

### 2. Kích hoạt Project
```
Activate the project /workspaces/autotrader
```

### 3. Sử dụng Serena Tools
```
# Phân tích cấu trúc
Get symbols overview for /workspaces/autotrader

# Tìm kiếm code
Search for pattern "def trading" in /workspaces/autotrader

# Đọc và phân tích file
Read file /workspaces/autotrader/main.py

# Tìm symbols
Find symbol "TradingStrategy" in /workspaces/autotrader

# Tìm references
Find referencing symbols for "execute_trade" in /workspaces/autotrader/src/trading.py
```

## Tính năng đặc biệt với IDE Context

Khi sử dụng context `ide-assistant`, Serena sẽ:
- Tối ưu hóa cho IDE workflow
- Cung cấp code suggestions phù hợp
- Tích hợp tốt với editor features
- Hỗ trợ refactoring và code navigation

## Workflow đề xuất

### 1. Setup
```bash
# Khởi động Serena
./start-serena.sh start

# Kiểm tra status
./start-serena.sh status
```

### 2. Trong Augment Code
1. Mở project autotrader
2. Kích hoạt Serena MCP
3. Activate project: `Activate the project /workspaces/autotrader`

### 3. Làm việc với code
- Sử dụng Serena để phân tích code
- Tận dụng semantic search và navigation
- Sử dụng AI suggestions với context từ Serena

## Troubleshooting

### Nếu không kết nối được:
1. Kiểm tra Serena server: `./start-serena.sh status`
2. Kiểm tra port 9121 có available không
3. Restart Augment Code
4. Kiểm tra logs: `./start-serena.sh logs`

### Nếu tools không hiển thị:
1. Refresh MCP servers trong Augment Code
2. Kiểm tra extension settings
3. Restart MCP connection

## Lưu ý quan trọng

- Sử dụng context `ide-assistant` thay vì `desktop-app`
- Đảm bảo Docker đang chạy
- Volume mounts phải chính xác
- Restart IDE sau khi thay đổi cấu hình MCP
