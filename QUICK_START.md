# Serena MCP - Quick Start Guide

## 🚀 Khởi động nhanh

### 1. Start Serena MCP Server
```bash
./start-serena.sh start
```

### 2. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> Desktop
Copy nội dung từ `claude_desktop_config.json` v<PERSON><PERSON> config:
- File > Settings > Developer > MCP Servers > Edit Config
- Restart Claude Desktop

### 3. Sử dụng trong Claude
```
Activate the project /workspaces/autotrader
```

## 📊 Truy cập
- **Dashboard**: http://localhost:24282/dashboard/
- **MCP Server**: http://localhost:9121

## 🛠️ Qu<PERSON>n lý
```bash
./start-serena.sh status    # Kiểm tra status
./start-serena.sh logs      # Xem logs  
./start-serena.sh restart   # Restart
./start-serena.sh stop      # Dừng server
```

## 📁 Projects được mount
- `/Users/<USER>/Work/solashi` → `/workspaces/projects`
- `/Users/<USER>/Work/solashi/autotrader` → `/workspaces/autotrader`

## 💡 V<PERSON> dụ sử dụng
```
# Phân tích dự án
Get symbols overview for /workspaces/autotrader/src

# Tìm kiếm code
Search for pattern "trading" in /workspaces/autotrader

# Đọc file
Read file /workspaces/autotrader/main.py
```

Chi tiết hơn xem trong `AUTOTRADER_SETUP.md`
