# Serena MCP - Quick Start Guide

## 🚀 Khởi động nhanh

### <PERSON>
```bash
./start-serena.sh start
```
Copy config từ `claude_desktop_config.json` → Claude Desktop settings

### Cho IDE (Augment Code, Cursor)
```bash
./start-serena-ide.sh start
```
Xem hướng dẫn cấu hình: `./start-serena-ide.sh config`

### Sử dụng
```
Activate the project /workspaces/autotrader
```

## 📊 Truy cập
- **Dashboard**: http://localhost:24282/dashboard/
- **MCP Server**: http://localhost:9121

## 🛠️ Quản lý
```bash
./start-serena.sh status    # Kiểm tra status
./start-serena.sh logs      # Xem logs  
./start-serena.sh restart   # Restart
./start-serena.sh stop      # Dừng server
```

## 📁 Projects được mount
- `/Users/<USER>/Work/solashi` → `/workspaces/projects`
- `/Users/<USER>/Work/solashi/autotrader` → `/workspaces/autotrader`

## 💡 Ví dụ sử dụng
```
# Phân tích dự án
Get symbols overview for /workspaces/autotrader/src

# Tìm kiếm code
Search for pattern "trading" in /workspaces/autotrader

# Đọc file
Read file /workspaces/autotrader/main.py
```

Chi tiết hơn xem trong `AUTOTRADER_SETUP.md`
