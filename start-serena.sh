#!/bin/bash

# Serena MCP Server Management Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker Desktop first."
        exit 1
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start Serena MCP server (production mode)"
    echo "  dev       Start Serena MCP server (development mode)"
    echo "  stop      Stop Serena MCP server"
    echo "  restart   Restart Serena MCP server"
    echo "  logs      Show <PERSON> logs"
    echo "  status    Show Serena status"
    echo "  build     Build Serena Docker image"
    echo "  clean     Clean up Docker containers and images"
    echo "  help      Show this help message"
}

# Function to start Serena
start_serena() {
    print_status "Starting Serena MCP server..."
    check_docker
    
    # Check if already running
    if docker compose ps serena | grep -q "Up"; then
        print_warning "Serena is already running!"
        return 0
    fi
    
    docker compose up -d serena
    
    # Wait a moment for startup
    sleep 3
    
    # Check if started successfully
    if docker compose ps serena | grep -q "Up"; then
        print_success "Serena MCP server started successfully!"
        print_status "Dashboard: http://localhost:${SERENA_DASHBOARD_PORT:-24282}/dashboard/"
        print_status "MCP Server: http://localhost:${SERENA_PORT:-9121}"
    else
        print_error "Failed to start Serena MCP server"
        docker compose logs serena
        exit 1
    fi
}

# Function to start development mode
start_dev() {
    print_status "Starting Serena MCP server in development mode..."
    check_docker
    
    if docker compose ps serena-dev | grep -q "Up"; then
        print_warning "Serena dev is already running!"
        return 0
    fi
    
    docker compose up -d serena-dev
    sleep 3
    
    if docker compose ps serena-dev | grep -q "Up"; then
        print_success "Serena MCP server (dev) started successfully!"
        print_status "Dashboard: http://localhost:${SERENA_DASHBOARD_PORT:-24282}/dashboard/"
        print_status "MCP Server: http://localhost:${SERENA_PORT:-9121}"
    else
        print_error "Failed to start Serena MCP server (dev)"
        docker compose logs serena-dev
        exit 1
    fi
}

# Function to stop Serena
stop_serena() {
    print_status "Stopping Serena MCP server..."
    docker compose down
    print_success "Serena MCP server stopped"
}

# Function to restart Serena
restart_serena() {
    print_status "Restarting Serena MCP server..."
    stop_serena
    sleep 2
    start_serena
}

# Function to show logs
show_logs() {
    print_status "Showing Serena logs..."
    docker compose logs -f serena
}

# Function to show status
show_status() {
    print_status "Serena MCP server status:"
    docker compose ps
    echo ""
    print_status "Port usage:"
    lsof -i :${SERENA_PORT:-9121} 2>/dev/null || echo "Port ${SERENA_PORT:-9121} is free"
    lsof -i :${SERENA_DASHBOARD_PORT:-24282} 2>/dev/null || echo "Port ${SERENA_DASHBOARD_PORT:-24282} is free"
}

# Function to build image
build_image() {
    print_status "Building Serena Docker image..."
    docker compose build
    print_success "Serena Docker image built successfully"
}

# Function to clean up
clean_up() {
    print_status "Cleaning up Docker containers and images..."
    docker compose down --rmi all --volumes --remove-orphans
    print_success "Cleanup completed"
}

# Main script logic
case "${1:-help}" in
    start)
        start_serena
        ;;
    dev)
        start_dev
        ;;
    stop)
        stop_serena
        ;;
    restart)
        restart_serena
        ;;
    logs)
        show_logs
        ;;
    status)
        show_status
        ;;
    build)
        build_image
        ;;
    clean)
        clean_up
        ;;
    help|--help|-h)
        show_usage
        ;;
    *)
        print_error "Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
